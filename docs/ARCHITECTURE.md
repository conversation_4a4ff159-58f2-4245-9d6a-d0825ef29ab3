# FlyFit Architecture

This document provides a comprehensive overview of the FlyFit application architecture, design patterns, technical decisions, and system components. **Use this as your definitive guide to understanding how FlyFit works at every level.**

> [!NOTE]
> **Living Document**: This architecture documentation is actively maintained and reflects the current state of the system. Last updated: May 2025.

## Table of Contents

1. [System Overview](#system-overview)
2. [High-Level Architecture](#high-level-architecture)
3. [Mobile App Architecture](#mobile-app-architecture)
4. [Backend Architecture](#backend-architecture)
5. [Data Architecture](#data-architecture)
6. [External Integrations](#external-integrations)
7. [Security & Authentication](#security--authentication)
8. [Development Patterns](#development-patterns)

## System Overview

### What is FlyFit?

FlyFit is a **comprehensive fitness and wellness platform** that combines:
- **Mobile-first experience** - React Native app for iOS and Android
- **Social fitness challenges** - Team-based competitions and leaderboards
- **Trainer-client relationships** - Workout delivery and progress tracking
- **Wellness content** - Daily blogs, quizzes, and motivational quotes
- **Health data integration** - Apple Health and Google Fit synchronization
- **Organization management** - Company-wide wellness programs

### System Components

FlyFit consists of **multiple interconnected applications and services**:

1. **📱 Mobile App** - Primary user interface (React Native + Expo)
2. **🌐 Web Sign-up** - User registration and invite handling (React + Vite)
3. **🔗 Link Redirect** - Deep linking and app installation flow (React)
4. **⚡ Backend Functions** - API endpoints and business logic (Node.js + Firebase)
5. **📊 PDF Reports** - Challenge summaries and analytics (React PDF)
6. **🔍 Search Service** - User discovery (Algolia)
7. **📧 Email Service** - Transactional emails (Resend)
8. **💾 Database** - Data persistence (Firestore)
9. **🔐 Authentication** - User management (Firebase Auth)

### Data Flow Overview

**How data moves through the system:**

```mermaid
graph TD
    subgraph "Client Layer"
        MA[Mobile App]
        WS[Web Sign-up]
        LR[Link Redirect]
    end

    subgraph "API Layer"
        CF[Cloud Functions]
        FS[Firestore]
    end

    subgraph "External Services"
        AH[Apple Health]
        GF[Google Health Connect]
        AL[Algolia Search]
        RS[Resend Email]
        FB[Fitbit API]
    end

    subgraph "Static Content"
        WC[Wellness Content]
        AC[App Config]
    end

    MA <--> CF
    MA <--> FS
    MA <--> AH
    MA <--> GF
    MA <--> FB

    WS --> CF
    LR --> CF

    CF --> FS
    CF --> AL
    CF --> RS

    CF --> WC
    CF --> AC
```

### Technology Stack

**Frontend Technologies:**
- **React Native 0.76** - Cross-platform mobile development
- **Expo SDK 52** - Development tooling and native modules
- **TypeScript** - Type safety and developer experience
- **React Navigation 7** - Screen navigation and routing
- **Jotai** - Global state management
- **React Query** - Server state management and caching
- **React Native Paper** - UI component library
- **React Native Reanimated** - Smooth animations

**Backend Technologies:**
- **Firebase Functions** - Serverless API endpoints
- **Firestore** - NoSQL document database
- **Firebase Auth** - User authentication and authorization
- **Firebase Storage** - File storage and CDN
- **Firebase Hosting** - Static site hosting
- **Node.js 22** - Runtime environment
- **Express.js** - HTTP server framework
- **TypeScript** - Type safety across the stack

**External Services:**
- **Algolia** - Full-text search for user discovery
- **Resend** - Transactional email delivery
- **Apple Health** - iOS health data integration
- **Google Health Connect** - Android health data integration
- **Fitbit API** - Wearable device data sync
- **EAS (Expo Application Services)** - Build and deployment

## High-Level Architecture

### System Architecture Overview

![FlyFit Architecture Diagram](./fly-fit-architecture-diagram.png)

**Architecture Components Explained:**

**Core Technologies:**
- **Node.js** - Backend runtime for Firebase Functions and server-side logic
- **React** - Frontend framework for web applications and component architecture
- **TypeScript** - Type-safe development ensuring consistency across the entire stack
- **React Native** - Cross-platform mobile app development with React

**Mobile & Distribution (Bottom):**
- **iOS/Android Apps** - React Native mobile applications distributed through app stores
- **Wearable Integration** - Direct connection to fitness devices (Apple Watch, Fitbit, etc.)
- **App Store Distribution** - Apple App Store and Google Play for user acquisition

**Google Cloud Platform (Center - Dashed Box):**
- **Cloud Functions** - Serverless API endpoints handling business logic and integrations
- **Firestore** - Real-time NoSQL database storing all application data
- **Authentication & IAM** - User identity management and role-based access control
- **Object Storage** - File storage for images, documents, and media content

**External Services (Right Side):**
- **Resend Email** - Transactional email delivery for notifications and communications
- **Algolia Search** - Powerful user discovery and search functionality
- **PDF Generation** - Challenge reports and analytics document creation

### Architecture Principles

**1. Mobile-First Design**
- Primary experience is in the React Native mobile app
- Web applications serve specific use cases (sign-up, redirects)
- Responsive design ensures consistent experience across devices

**2. Serverless Backend**
- Firebase Functions provide scalable, event-driven API endpoints
- No server management or infrastructure concerns
- Automatic scaling based on demand

**3. Real-time Data Synchronization**
- Firestore real-time listeners keep data synchronized across devices
- React Query manages client-side caching and background updates
- Optimistic updates provide immediate user feedback

**4. Modular Architecture**
- Clear separation between mobile app, web apps, and backend services
- Independent deployment of different components
- Shared TypeScript types ensure consistency across modules

**5. External Service Integration**
- Health data synced from native platform APIs
- Search powered by specialized service (Algolia)
- Email delivery handled by dedicated service (Resend)
- Each service optimized for its specific purpose

### Repository Structure

**Monorepo Organization:**
```
fly-fit/
├── 📱 src/                         # React Native Mobile App
│   ├── components/                 # Feature-specific UI components
│   │   ├── Auth/                   # Authentication flows
│   │   ├── Challenges/             # Challenge management
│   │   ├── HealthSync/             # Health data integration
│   │   ├── Admin/                  # Administrative features
│   │   └── Wellness/               # Content and streaks
│   ├── pages/                      # Screen components (navigation targets)
│   ├── contexts/                   # State management and data hooks
│   ├── hooks/                      # Reusable React hooks
│   ├── types/                      # TypeScript type definitions
│   ├── utils/                      # Utility functions and business logic
│   └── base-components/            # Atomic UI components
│
├── ⚡ functions/                   # Firebase Backend Services
│   ├── src/                        # Cloud Functions source code
│   │   ├── endpoints/              # HTTP API endpoints
│   │   ├── endpointHelpers/        # Business logic helpers
│   │   ├── types/                  # Backend type definitions
│   │   └── utils/                  # Server-side utilities
│   ├── emails/                     # Email templates (React Email)
│   ├── report-generation/          # PDF generation service
│   └── swagger-docs/               # API documentation (unmaintained)
│
├── 🌐 web-sign-up/                  # Web Registration Application
│   └── src/                        # React web app source
│
├── 📊 data/                        # Static Content and Configuration
│   ├── config/                     # Environment-specific settings
│   └── wellness/                   # Static wellness content
│
├── 🛠️ scripts/                     # Automation and Utilities
│   ├── ci-cd*.zx.mjs               # Deployment automation
│   ├── import-*.zx.mjs             # Data import tools
│   └── *.appsScript.mjs            # Google Apps Script integrations
│
├── 📚 docs/                        # Documentation
├── 🎨 assets/                      # Static assets and media
└── 📋 Configuration Files          # Package.json, Firebase config, etc.
```

## Mobile App Architecture

### Application Layer Architecture

The React Native mobile app follows a **layered architecture** with clear separation of concerns:

```mermaid
graph TD
    subgraph "Presentation Layer"
        P[Pages/Screens]
        C[Components]
        BC[Base Components]
    end

    subgraph "State Management Layer"
        J[Jotai Atoms<br/>Global State]
        RC[React Context<br/>Feature State]
        RQ[React Query<br/>Server State]
    end

    subgraph "Business Logic Layer"
        H[Custom Hooks<br/>Business Logic]
        DH[Data Hooks<br/>Firebase Queries]
        U[Utils<br/>Domain Logic]
    end

    subgraph "Data Access Layer"
        API[API Layer<br/>Firebase SDK]
        EXT[External APIs<br/>Health, Fitbit]
    end

    P --> C
    C --> BC

    P --> J
    P --> RC
    C --> RQ

    P --> H
    C --> H
    H --> DH
    H --> U

    DH --> API
    H --> EXT
```

**1. Presentation Layer**
- **Pages/Screens** - Top-level navigation targets
- **Components** - Feature-specific UI components
- **Base Components** - Reusable atomic UI elements

**2. State Management Layer**
- **Jotai Atoms** - Global application state (user preferences, app settings)
- **React Context** - Feature-specific state (auth, navigation)
- **React Query** - Server state management with caching

**3. Business Logic Layer**
- **Custom Hooks** - Encapsulated business logic and side effects
- **Data Hooks** - Firebase query abstractions
- **Utils** - Pure functions for domain logic

**4. Data Access Layer**
- **API Layer** - Firebase SDK abstractions
- **External APIs** - Health data and third-party integrations

### Component Architecture Patterns

**Component Hierarchy:**
```mermaid
graph TD
    subgraph "Screen Level"
        S[Screen Component<br/>Navigation Target]
    end

    subgraph "Feature Level"
        FC[Feature Container<br/>Business Logic]
        FP[Feature Presenter<br/>UI Logic]
    end

    subgraph "Component Level"
        CC[Composite Component<br/>Multiple Concerns]
        SC[Simple Component<br/>Single Concern]
    end

    subgraph "Base Level"
        BC[Base Component<br/>Atomic UI Element]
    end

    S --> FC
    FC --> FP
    FP --> CC
    CC --> SC
    SC --> BC
```

**Component Composition Example:**
```tsx
// Screen Level - Navigation target
const ViewChallengeScreen = () => (
  <ViewChallengeContainer challengeId={challengeId} />
);

// Feature Level - Business logic container
const ViewChallengeContainer = ({challengeId}) => {
  const challenge = useChallenge(challengeId);
  const user = useAppUser();

  return (
    <ViewChallengePresenter
      challenge={challenge}
      user={user}
    />
  );
};

// Feature Level - UI presentation
const ViewChallengePresenter = ({challenge, user}) => (
  <>
    <ChallengeHeader challenge={challenge} />
    <ChallengeProgress challenge={challenge} user={user} />
    <ChallengeLeaderboard challenge={challenge} />
  </>
);

// Component Level - Composite components
const ChallengeHeader = ({challenge}) => (
  <Box>
    <ChallengeImage source={challenge.imageUrl} />
    <ChallengeTitle text={challenge.title} />
    <ChallengeStatus status={challenge.status} />
  </Box>
);

// Base Level - Atomic components
const ChallengeTitle = ({text}) => (
  <Text variant="headlineMedium">{text}</Text>
);
```

## Development Patterns

### State Management Patterns

**State Management Strategy:**
```mermaid
graph TD
    subgraph "Global State (Jotai)"
        GS1[User Preferences]
        GS2[App Settings]
        GS3[Feature Flags]
        GS4[Navigation State]
    end

    subgraph "Feature State (React Context)"
        FS1[Auth Context]
        FS2[Theme Context]
        FS3[Snack Context]
        FS4[Modal Context]
    end

    subgraph "Server State (React Query)"
        SS1[User Data]
        SS2[Challenge Data]
        SS3[Health Data]
        SS4[Wellness Content]
    end

    subgraph "Local State (useState)"
        LS1[Form State]
        LS2[UI State]
        LS3[Component State]
    end

    GS1 --> FS1
    FS1 --> SS1
    SS1 --> LS1
```

**Custom Hook Pattern:**
```typescript
// Business Logic Hook
export const useChallenge = (challengeId: string) => {
  // Data fetching
  const {data: challenge, isLoading} = useFirestoreQuery({
    q: () => query(db.challenges, where('id', '==', challengeId)),
    queryKey: ['challenge', challengeId],
    collectionReference: db.challenges,
  });

  // Derived state
  const isActive = useMemo(() =>
    challenge?.status === 'active' &&
    challenge?.endDate > new Date()
  , [challenge]);

  // Mutation for joining challenge
  const joinChallengeMutation = useMutation({
    mutationFn: async () => {
      if (!challenge) throw new Error('No challenge found');
      return await firebaseApi.joinChallenge(challenge.id);
    },
    onSuccess: () => {
      // Invalidate related queries to refresh data
      queryClient.invalidateQueries(['challenge', challengeId]);
      queryClient.invalidateQueries(['challengeParticipants', challengeId]);
    },
  });

  return {
    challenge,
    isLoading,
    isActive,
    joinChallenge: joinChallengeMutation.mutate,
    isJoining: joinChallengeMutation.isPending,
    joinError: joinChallengeMutation.error,
  };
};
```

**Data Hook Pattern:**
```typescript
// Firebase Query Hook
export const useChallengeParticipants = (challengeId: string) => {
  return useFirestoreQuery({
    q: () => query(
      db.challengeParticipants,
      where('challengeId', '==', challengeId),
      orderBy('joinedAt', 'desc')
    ),
    queryKey: ['challengeParticipants', challengeId],
    isEnabled: !!challengeId,
    collectionReference: db.challengeParticipants,
  });
};
```

**Jotai Atom Pattern:**
```typescript
// Global State Atom
const userPreferencesAtom = atom<UserPreferences>({
  theme: 'light',
  notifications: true,
  units: 'metric',
});

// Derived Atom
const isDarkModeAtom = atom(
  (get) => get(userPreferencesAtom).theme === 'dark'
);

// Write-only Atom
const toggleThemeAtom = atom(
  null,
  (get, set) => {
    const current = get(userPreferencesAtom);
    set(userPreferencesAtom, {
      ...current,
      theme: current.theme === 'light' ? 'dark' : 'light'
    });
  }
);

// Hook Wrappers (never export atoms directly)
export const useUserPreferences = () => useAtomValue(userPreferencesAtom);
export const useIsDarkMode = () => useAtomValue(isDarkModeAtom);
export const useToggleTheme = () => useSetAtom(toggleThemeAtom);
```

### Custom Hook Guidelines

**When to create a custom hook:**
- ✅ Complex business logic that could be reused
- ✅ Data fetching with transformation or caching
- ✅ Side effects that need cleanup
- ✅ State management that spans multiple components

**When NOT to create a custom hook:**
- ❌ Simple one-off logic that won't be reused
- ❌ Just wrapping another hook without added value
- ❌ Heavy reliance on local component state
- ❌ Direct UI configuration (like `useAppTheme`)

## Backend Architecture

### Firebase Functions Architecture

**Function Organization:**
```mermaid
graph TD
    subgraph "HTTP Endpoints"
        EP1[User Management<br/>signUp, login, profile]
        EP2[Challenge Management<br/>create, join, update]
        EP3[Health Data<br/>sync, aggregate, report]
        EP4[Content Management<br/>wellness, notifications]
    end

    subgraph "Scheduled Functions"
        SF1[Daily Aggregation<br/>health data rollup]
        SF2[Challenge Processing<br/>leaderboard updates]
        SF3[Content Delivery<br/>daily wellness content]
        SF4[Cleanup Tasks<br/>expired data removal]
    end

    subgraph "Triggered Functions"
        TF1[User Created<br/>setup default data]
        TF2[Challenge Updated<br/>notification triggers]
        TF3[Health Data Changed<br/>streak calculations]
        TF4[Organization Changes<br/>permission updates]
    end

    subgraph "Shared Services"
        SS1[Authentication<br/>JWT validation]
        SS2[Database Access<br/>Firestore operations]
        SS3[External APIs<br/>email, search, health]
        SS4[Business Logic<br/>domain calculations]
    end

    EP1 --> SS1
    EP2 --> SS2
    EP3 --> SS3
    EP4 --> SS4

    SF1 --> SS2
    SF2 --> SS4
    SF3 --> SS3
    SF4 --> SS2

    TF1 --> SS2
    TF2 --> SS3
    TF3 --> SS4
    TF4 --> SS1
```

**Function Structure Pattern:**
```typescript
// Real endpoint example: functions/src/endpoints/requests/challenge/getChallengeEmails.ts
export const getChallengeEmails = onFirebaseRequest(
  composeMiddleware(
    'getChallengeEmails',
    async (request, response) => {
      // 1. Input Validation
      const {challengeId} = request.body as Record<string, UUIDString | undefined>;
      if (!isDefinedString(challengeId)) {
        LOGGER.warn('challengeId is required', request);
        response.status(StatusCodes.BAD_REQUEST_400).send('challengeId is required').end();
        return;
      }

      // 2. Data Fetching & Validation
      const challenge = await getChallengeById(challengeId);
      if (!challenge) {
        LOGGER.warn(`No challenge found for id ${challengeId}`);
        response
          .status(StatusCodes.BAD_REQUEST_400)
          .send(`No challenge found for id ${challengeId}`)
          .end();
        return;
      }

      // 3. Business Logic
      const userIds = challenge.participantIds;
      const appUsers = await getAppUsersByIds(userIds);
      const emails = [...appUsers.values()].map(appUser => appUser.email);

      // 4. Response Validation & Return
      if (isEmptyArray(emails)) {
        response.status(StatusCodes.NOT_FOUND_404).send('user emails not found').end();
        return;
      }

      response.status(StatusCodes.OK_200).send(emails.join(',')).end();
    },
    // 5. Middleware Stack (runs in order)
    withValidPostRequest,  // Validates POST request structure
    withApiKey('************************************'), // Validates API key
  ),
);
```

**Key Patterns:**
- **`onFirebaseRequest`** - Wrapper that handles try/catch, logging, and error handling automatically
- **`composeMiddleware`** - Combines the main function with middleware helpers
- **Middleware stack** - Listed at the bottom, executed in order before the main function
- **Early returns** - Explicit error handling with appropriate status codes
- **Firestore helpers** - Use abstracted functions like `getChallengeById` and `getAppUsersByIds`
- **Type safety** - TypeScript interfaces for request/response validation

## Data Architecture

### Firestore Database Design

**Database Structure Philosophy:**
- **Document-oriented** - Each entity is a document with embedded data
- **Mostly normalized** - Independent entities are documents, linked together by their IDs, and related data is in subcollections
- **Collection-based** - Related data grouped in collections and subcollections
- **Real-time** - Firestore listeners provide live updates in the app

**Detailed Data Model:**

```mermaid
erDiagram
    %% Core User Management
    AppUser {
        UUIDString id PK
        UUIDString[] organizationIds FK
    }

    BaseAppUser {
        UUIDString id PK
        UUIDString[] organizationIds FK
    }

    HealthData {
        IsoMonth id PK "yyyy-MM format"
    }

    AppUserNotificationDocument {
        string id PK "always 'default'"
    }

    AppUserMetadata {
        DocumentIdType id PK "healthData|streakDebounce|healthDataDebounce"
    }

    %% Organization Management
    Organization {
        UUIDString id PK
        UUIDString[] adminIds FK
        UUIDString[] coachIds FK
        UUIDString[] clientIds FK
        UUIDString parentOrganizationId FK
        UUIDString[] childOrganizationIds FK
    }

    OrganizationPost {
        UUIDString id PK
        UUIDString organizationId FK
    }

    %% Challenge System
    Challenge {
        UUIDString id PK
        ChallengeGroupingType groupingType "INDIVIDUAL|TEAMS|GROUPS"
        UUIDString[] participantIds FK "for INDIVIDUAL/TEAMS"
        UUIDString rootGroupId FK "for GROUPS only"
    }

    ChallengeGroupDocument {
        UUIDString id PK
        UUIDString challengeId FK
        UUIDString parentGroupId FK
        UUIDString[] groupIds FK
    }

    ChallengeGroupParticipantDocument {
        UUIDString id PK
        UUIDString challengeId FK
        UUIDString[] groupIds FK
        UUIDString teamId FK "for teams challenges"
    }

    %% Challenge Posts
    ChallengePostTeams {
        UUIDString id PK
        UUIDString challengeId FK
        UUIDString teamId FK "null for global posts"
    }

    ChallengePostGroups {
        UUIDString id PK
        UUIDString challengeId FK
        UUIDString groupId FK "null for global posts"
    }

    %% Workouts and Meals
    Workout {
        UUIDString id PK
        UUIDString parentWorkoutId FK
        UUIDString copyWorkoutId FK
        string[] childrenWorkoutIds FK
    }

    Meal {
        UUIDString id PK
        UUIDString copiedMealId FK
    }

    %% Wellness Content
    WellnessBlog {
        string id PK
    }

    WellnessQuiz {
        DocumentIdType id PK
    }

    WellnessQuote {
        string id PK
    }

    %% Streaks and Progress
    MovementStreakDocument {
        SnakeCase2Id id PK "userId_isoDate"
    }

    QuizStreakDocument {
        SnakeCase2Id id PK "userId_isoDate"
        string quizId FK
    }

    %% Reports and Analytics
    SummaryReport {
        UUIDString id PK
        UUIDString creatorOrganizationId FK
        UUIDString challengeId FK
    }

    %% Authentication and Invites
    AuthToken {
        UUIDString userId PK
    }

    InviteCodeDocument {
        InviteCode id PK "short token"
    }

    %% Configuration
    AppConfig {
        string id PK "always 'default'"
    }

    AppUpdateDocument {
        string id PK "ios|android"
    }

    %% Meaningful Relationships Only (no userId links)
    AppUser ||--o{ HealthData : "subcollection"
    AppUser ||--o{ AppUserNotificationDocument : "subcollection"
    AppUser ||--o{ AppUserMetadata : "subcollection"
    AppUser ||--|| AuthToken : "keyed by userId"

    Organization ||--o{ OrganizationPost : "contains posts"
    Organization ||--o{ Organization : "parent/child hierarchy"

    Challenge ||--o{ ChallengeGroupDocument : "contains groups"
    Challenge ||--o{ ChallengeGroupParticipantDocument : "has participants"
    Challenge ||--o{ ChallengePostTeams : "contains team posts"
    Challenge ||--o{ ChallengePostGroups : "contains group posts"

    ChallengeGroupDocument ||--o{ ChallengeGroupDocument : "parent/child hierarchy"

    Workout ||--o{ Workout : "parent/child hierarchy"
    Workout ||--|| Workout : "copied from"

    Meal ||--|| Meal : "copied from"

    WellnessQuiz ||--o{ QuizStreakDocument : "quiz completions"

    Challenge ||--o{ SummaryReport : "generates reports"
    Organization ||--o{ SummaryReport : "org reports"

    BaseAppUser ||--|| AppUser : "algolia search index"
```

## External Integrations

### Health Sync Architecture

```mermaid
graph TD
    subgraph "Mobile App"
        HS[Health Sync Service]
    end

    subgraph "React Native Libraries"
        RNH[RN HealthKit]
        EHC[Expo Health Connect]
    end

    subgraph "iOS Platform"
        AH[Apple Health]
        HK[HealthKit Framework]
    end

    subgraph "Android Platform"
        HC[Health Connect]
        SH[Samsung Health]
        GF[Google Fit]
    end

    subgraph "Wearables"
        FB[Fitbit API]
        AW[Apple Watch]
        WW[Wear OS]
        GM[Garmin]
    end

    subgraph "Backend"
        FS[Firestore]
        HD[Health Data Collection]
    end

    HS --> RNH
    HS --> EHC
    HS --> FB
    HS --> FS

    RNH --> HK
    EHC --> HC

    HK --> AH
    SH --> HC
    GF --> HC

    AW --> AH
    WW --> HC
    GM --> AH
    GM --> HC

    HD --> FS
```

**Health Data Flow:**
1. **Wearable Data Collection** - Devices (Apple Watch, Garmin, Wear OS) sync to platform health stores
2. **Platform Aggregation** - Apple Health and Health Connect aggregate data from multiple sources
3. **React Native Bridge** - RN HealthKit and Expo Health Connect libraries provide JavaScript interfaces
4. **Permission Management** - User grants specific data access permissions through native dialogs
5. **Health Sync Service** - Background sync pulls latest health data via React Native libraries
6. **Direct Firestore Sync** - Health data written directly to Firestore via Firebase SDK (bypasses Cloud Functions)
7. **Health Data Store** - Dedicated Firestore collection stores all health metrics and history
8. **Real-time Updates** - UI updates with latest health metrics via Firestore listeners

**Key Architecture Notes:**
- **Direct Database Access** - Mobile app writes health data directly to Firestore via the SDK
- **React Native Abstraction** - Libraries handle platform-specific health API differences
- **Multi-source Support** - Garmin devices can sync through both iOS and Android platforms
- **Samsung Integration** - Samsung Health feeds into Health Connect on Android devices

### Search Integration (Algolia)

**Search Architecture:**
```mermaid
graph LR
    subgraph "Data Sources"
        AU[AppUser Collection]
        BAU[BaseAppUser Collection]
    end

    subgraph "Firebase Backend"
        CF[Search API<br/>Cloud Function]
        EXT[Firebase-Algolia<br/>Extension]
    end

    subgraph "Algolia"
        AI[Search Index]
        AS[Algolia API]
    end

    subgraph "Mobile App"
        SC[Search Component]
        SR[Search Results]
        CC[Client Cache]
    end

    AU --> BAU
    BAU --> EXT
    EXT --> AI

    SC --> CF
    CF --> AS
    AS --> CF
    CF --> SR
    SR --> CC
```

**Search Flow:**
1. **Data Sync** - AppUser changes trigger BaseAppUser updates with selected fields
2. **Automatic Indexing** - Firebase-Algolia extension syncs BaseAppUser to search index
3. **Search Request** - Mobile app calls Firebase search function with query
4. **Algolia Query** - Cloud function queries Algolia API with private key
5. **Results Processing** - Function returns search hits to mobile app
6. **Client Caching** - App caches and manages search results locally

**Search Features:**
- **User Discovery** - Find users by name, email, or organization
- **Real-time Sync** - User profile changes automatically update search index
- **Faceted Search** - Filter by organization, role, or activity level
- **Typo Tolerance** - Fuzzy matching for user-friendly search

### Email Service (Resend)

**Email Architecture:**
```mermaid
graph TD
    subgraph "Triggers"
        UT[User Actions]
        ST[System Events]
        SF[Scheduled Functions]
    end

    subgraph "Email Service"
        CF[Cloud Functions]
        ET[Email Templates]
        EV[Email Validation]
    end

    subgraph "Resend API"
        RS[Resend Service]
        ED[Email Delivery]
    end

    subgraph "Recipients"
        U[Users]
        T[Trainers]
        A[Admins]
    end

    UT --> CF
    ST --> CF
    SF --> CF

    CF --> ET
    CF --> EV
    CF --> RS

    RS --> ED
    ED --> U
    ED --> T
    ED --> A
```

**Email Types:**
- **Transactional** - Welcome emails, password resets, notifications
- **Challenge Updates** - Join confirmations, progress reports, completions
- **Wellness Content** - Daily quotes, blog notifications, quiz reminders
- **Administrative** - User invitations, organization updates, system alerts

## Security & Authentication

### Authentication Flow

**Firebase Auth Integration:**
```mermaid
sequenceDiagram
    participant U as User
    participant A as Mobile App
    participant FA as Firebase Auth
    participant CF as Cloud Functions
    participant FS as Firestore

    U->>A: Login Request
    A->>FA: Authenticate User
    FA->>A: ID Token + Refresh Token
    A->>CF: API Request + ID Token
    CF->>FA: Verify Token
    FA->>CF: User Claims
    CF->>FS: Authorized Query
    FS->>CF: Data Response
    CF->>A: API Response
    A->>U: Display Data
```

**Security Layers:**
1. **Firebase Authentication** - Identity verification and token management
2. **Custom Claims** - Role-based permissions (admin, coach, user)
3. **Firestore Rules** - Document-level access control
4. **Function Authorization** - API endpoint permission checks
5. **Client Validation** - Input sanitization and validation

### Role-Based Access Control (RBAC)

The RBAC system was never fully developed, the app only uses the client/trainer roles for now.

## Technical Decisions & Trade-offs

### Architecture Decisions

**1. React Native + Expo Choice**
- ✅ **Pros**: Cross-platform development, rapid iteration, strong ecosystem
- ❌ **Cons**: Performance limitations, native module constraints
- **Decision**: Chosen for development speed and team expertise

**2. Firebase Backend Choice**
- ✅ **Pros**: Serverless scaling, real-time features, integrated services
- ❌ **Cons**: Vendor lock-in, query limitations, cost at scale
- **Decision**: Chosen for rapid development and built-in features

**3. Firestore Data Model**
- ✅ **Pros**: Real-time updates, offline support, flexible schema
- ❌ **Cons**: Query limitations, denormalization complexity, waterfall of reads
- **Decision**: Optimized for read performance over write efficiency

### Performance Considerations

**Mobile App Performance:**
- **Bundle size optimization** - Code splitting and lazy loading
- **Image optimization** - WebP format and responsive sizing
- **Memory management** - Proper cleanup of listeners and subscriptions
- **Network efficiency** - Request batching and caching strategies

**Backend Performance:**
- **Cold start optimization** - Function warming and connection pooling
- **Database indexing** - Composite indexes for complex queries
- **Caching strategies** - React Query client-side caching
- **Rate limiting** - API endpoint protection

### Scalability Patterns

**Horizontal Scaling:**
- **Stateless functions** - No server-side session storage
- **Database sharding** - Organization-based data partitioning
- **CDN distribution** - Global content delivery
- **Microservice architecture** - Independent service scaling

**Data Scaling:**
- **Denormalization** - Optimized read performance
- **Aggregation patterns** - Pre-computed summary data
- **Archive strategies** - Historical data management
- **Backup procedures** - Automated data protection

### Documentation Maintenance

This architecture document should be updated when:
- Major technology changes are implemented
- New external integrations are added
- Database schema changes significantly
- Security model is updated
- Performance optimizations are implemented

## Related Documentation

**For implementation guidance:**
- [**Contributing Guidelines**](./CONTRIBUTING.md) - Development workflow and coding standards
- [**Deployment Guide**](./DEPLOYMENT.md) - How to deploy architectural changes
- [**Operations Guide**](./OPERATIONS.md) - Monitoring and operational procedures

**For getting started:**
- [**Getting Started Guide**](./GETTING_STARTED.md) - Complete development environment setup
